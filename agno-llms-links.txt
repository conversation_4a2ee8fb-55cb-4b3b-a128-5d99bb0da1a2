# Agno

## Docs

- [Agent API](https://docs.agno.com/agent-api/introduction.md): A robust, production-ready application for serving Agents as an API.
- [A beautiful UI for your Agents](https://docs.agno.com/agent-ui/introduction.md): A beautiful, open-source interface for interacting with AI agents
- [Agent Context](https://docs.agno.com/agents/context.md)
- [What are Agents?](https://docs.agno.com/agents/introduction.md): Learn about Agno Agents and how they work.
- [Knowledge](https://docs.agno.com/agents/knowledge.md)
- [Memory](https://docs.agno.com/agents/memory.md)
- [Metrics](https://docs.agno.com/agents/metrics.md): Understanding agent run and session metrics in Agno
- [Multimodal Agents](https://docs.agno.com/agents/multimodal.md)
- [Prompts](https://docs.agno.com/agents/prompts.md)
- [Running your Agent](https://docs.agno.com/agents/run.md): Learn how to run an agent and get the response.
- [Sessions](https://docs.agno.com/agents/sessions.md)
- [Agent State](https://docs.agno.com/agents/state.md)
- [Session Storage](https://docs.agno.com/agents/storage.md)
- [Structured Output](https://docs.agno.com/agents/structured-output.md)
- [Agent Teams [Deprecated]](https://docs.agno.com/agents/teams.md)
- [Tools](https://docs.agno.com/agents/tools.md): Learn how to use tools in Agno to build AI agents.
- [User Control Flows](https://docs.agno.com/agents/user-control-flow.md): Learn how to control the flow of an agent's execution in Agno. This is also called "Human in the Loop".
- [AG-UI App](https://docs.agno.com/applications/ag-ui/introduction.md): Expose your Agno Agent as a AG-UI compatible app
- [Discord Bot](https://docs.agno.com/applications/discord/introduction.md): Host agents as Discord Bots.
- [FastAPI App](https://docs.agno.com/applications/fastapi/introduction.md): Host agents as FastAPI Applications.
- [Playground App](https://docs.agno.com/applications/playground/introduction.md): Host agents as Playground Applications.
- [Slack App](https://docs.agno.com/applications/slack/introduction.md): Host agents as Slack Applications.
- [Whatsapp App](https://docs.agno.com/applications/whatsapp/introduction.md): Host agents as Whatsapp Applications.
- [Product updates](https://docs.agno.com/changelog/overview.md)
- [Agentic Chunking](https://docs.agno.com/chunking/agentic-chunking.md)
- [Document Chunking](https://docs.agno.com/chunking/document-chunking.md)
- [Fixed Size Chunking](https://docs.agno.com/chunking/fixed-size-chunking.md)
- [Recursive Chunking](https://docs.agno.com/chunking/recursive-chunking.md)
- [Semantic Chunking](https://docs.agno.com/chunking/semantic-chunking.md)
- [AWS Bedrock Embedder](https://docs.agno.com/embedder/aws_bedrock.md)
- [Azure OpenAI Embedder](https://docs.agno.com/embedder/azure_openai.md)
- [Cohere Embedder](https://docs.agno.com/embedder/cohere.md)
- [Fireworks Embedder](https://docs.agno.com/embedder/fireworks.md)
- [Gemini Embedder](https://docs.agno.com/embedder/gemini.md)
- [HuggingFace Embedder](https://docs.agno.com/embedder/huggingface.md)
- [What are Embedders?](https://docs.agno.com/embedder/introduction.md): Learn how to use embedders with Agno to convert complex information into vector representations.
- [Mistral Embedder](https://docs.agno.com/embedder/mistral.md)
- [Ollama Embedder](https://docs.agno.com/embedder/ollama.md)
- [OpenAI Embedder](https://docs.agno.com/embedder/openai.md)
- [Qdrant FastEmbed Embedder](https://docs.agno.com/embedder/qdrant_fastembed.md)
- [SentenceTransformers Embedder](https://docs.agno.com/embedder/sentencetransformers.md)
- [Together Embedder](https://docs.agno.com/embedder/together.md)
- [Voyage AI Embedder](https://docs.agno.com/embedder/voyageai.md)
- [Simple Agent Evals](https://docs.agno.com/evals/introduction.md): Learn how to evaluate your Agno Agents and Teams across three key dimensions - accuracy (using LLM-as-a-judge), performance (runtime and memory), and reliability (tool calls).
- [Evals on the Agno platform](https://docs.agno.com/evals/platform.md): You can track your evaluation runs on the Agno platform
- [Books Recommender](https://docs.agno.com/examples/agents/books-recommender.md)
- [Finance Agent](https://docs.agno.com/examples/agents/finance-agent.md)
- [Movie Recommender](https://docs.agno.com/examples/agents/movie-recommender.md)
- [Recipe Creator](https://docs.agno.com/examples/agents/recipe-creator.md)
- [Research Agent](https://docs.agno.com/examples/agents/research-agent.md)
- [Research Agent using Exa](https://docs.agno.com/examples/agents/research-agent-exa.md)
- [Startup Analyst Agent](https://docs.agno.com/examples/agents/startup-analyst-agent.md): A sophisticated startup intelligence agent that leverages the `ScrapeGraph` Toolkit for comprehensive due diligence on companies
- [Teaching Assistant](https://docs.agno.com/examples/agents/teaching-assistant.md)
- [Travel Agent](https://docs.agno.com/examples/agents/travel-planner.md)
- [Tweet Analysis Agent](https://docs.agno.com/examples/agents/tweet-analysis-agent.md): An agent that analyzes tweets and provides comprehensive brand monitoring and sentiment analysis.
- [Youtube Agent](https://docs.agno.com/examples/agents/youtube-agent.md)
- [Agent with Tools](https://docs.agno.com/examples/applications/ag-ui/agent_with_tools.md): Expose your Agno Agent with tools as a AG-UI compatible app
- [Basic](https://docs.agno.com/examples/applications/ag-ui/basic.md): Expose your Agno Agent as a AG-UI compatible app
- [Research Team](https://docs.agno.com/examples/applications/ag-ui/team.md): Expose your Agno Team as a AG-UI compatible app
- [Agent with Media](https://docs.agno.com/examples/applications/discord/agent_with_media.md)
- [Agent with User Memory](https://docs.agno.com/examples/applications/discord/agent_with_user_memory.md)
- [Basic](https://docs.agno.com/examples/applications/discord/basic.md)
- [Basic](https://docs.agno.com/examples/applications/fastapi/basic.md): Expose your Agno Agent as a FastAPI app
- [Study Friend](https://docs.agno.com/examples/applications/fastapi/study_friend.md): Expose your Agno Agent as a FastAPI app
- [Agno Assist](https://docs.agno.com/examples/applications/playground/agno_assist.md)
- [Audio Conversation Agent](https://docs.agno.com/examples/applications/playground/audio_conversation_agent.md)
- [Basic](https://docs.agno.com/examples/applications/playground/basic.md)
- [Mcp Demo](https://docs.agno.com/examples/applications/playground/mcp_demo.md)
- [Multimodal Agents](https://docs.agno.com/examples/applications/playground/multimodal_agents.md)
- [Ollama Agents](https://docs.agno.com/examples/applications/playground/ollama_agents.md)
- [Reasoning Demo](https://docs.agno.com/examples/applications/playground/reasoning_demo.md)
- [Teams Demo](https://docs.agno.com/examples/applications/playground/teams_demo.md)
- [Upload Files](https://docs.agno.com/examples/applications/playground/upload_files.md)
- [Agent with User Memory](https://docs.agno.com/examples/applications/slack/agent_with_user_memory.md)
- [Basic](https://docs.agno.com/examples/applications/slack/basic.md)
- [Reasoning Agent](https://docs.agno.com/examples/applications/slack/reasoning_agent.md)
- [Agent With Media](https://docs.agno.com/examples/applications/whatsapp/agent_with_media.md)
- [Agent With User Memory](https://docs.agno.com/examples/applications/whatsapp/agent_with_user_memory.md)
- [Basic](https://docs.agno.com/examples/applications/whatsapp/basic.md)
- [Image Generation Model](https://docs.agno.com/examples/applications/whatsapp/image_generation_model.md)
- [Image Generation Tools](https://docs.agno.com/examples/applications/whatsapp/image_generation_tools.md)
- [Reasoning Agent](https://docs.agno.com/examples/applications/whatsapp/reasoning_agent.md)
- [Study Friend](https://docs.agno.com/examples/applications/whatsapp/study_friend.md)
- [Basic Async](https://docs.agno.com/examples/concepts/async/basic.md)
- [Data Analyst](https://docs.agno.com/examples/concepts/async/data_analyst.md)
- [Gather Multiple Agents](https://docs.agno.com/examples/concepts/async/gather_agents.md)
- [Reasoning Agent](https://docs.agno.com/examples/concepts/async/reasoning.md)
- [Structured Outputs](https://docs.agno.com/examples/concepts/async/structured_output.md)
- [Add Context](https://docs.agno.com/examples/concepts/context/01-add_context.md)
- [Agent Context](https://docs.agno.com/examples/concepts/context/02-agent_context.md)
- [Context In Instructions](https://docs.agno.com/examples/concepts/context/03-context_in_instructions.md)
- [Azure OpenAI Embedder](https://docs.agno.com/examples/concepts/embedders/azure-embedder.md)
- [Cohere Embedder](https://docs.agno.com/examples/concepts/embedders/cohere-embedder.md)
- [Fireworks Embedder](https://docs.agno.com/examples/concepts/embedders/fireworks-embedder.md)
- [Gemini Embedder](https://docs.agno.com/examples/concepts/embedders/gemini-embedder.md)
- [Huggingface Embedder](https://docs.agno.com/examples/concepts/embedders/huggingface-embedder.md)
- [Mistral Embedder](https://docs.agno.com/examples/concepts/embedders/mistral-embedder.md)
- [Ollama Embedder](https://docs.agno.com/examples/concepts/embedders/ollama-embedder.md)
- [OpenAI Embedder](https://docs.agno.com/examples/concepts/embedders/openai-embedder.md)
- [Qdrant FastEmbed Embedder](https://docs.agno.com/examples/concepts/embedders/qdrant-fastembed.md)
- [LanceDB Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/lancedb.md)
- [MilvusDB Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/milvusdb.md)
- [MongoDB Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/mongodb.md)
- [PgVector Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/pgvector.md)
- [QdrantDB Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/qdrantdb.md)
- [Weaviate Hybrid Search](https://docs.agno.com/examples/concepts/hybrid-search/weaviate.md)
- [ArXiv Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/arxiv-kb.md)
- [Combined Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/combined-kb.md)
- [CSV Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/csv-kb.md)
- [CSV URL Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/csv-url-kb.md)
- [Document Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/doc-kb.md)
- [DOCX Knowledge Base](https://docs.agno.com/examples/concepts/knowledge/docx-kb.md)
- [Agentic filtering with Docx](https://docs.agno.com/examples/concepts/knowledge/filters/docx/agentic_filtering.md): Learn how to do agentic knowledge filtering using Docx documents with user-specific metadata.
- [Filtering with Docx](https://docs.agno.com/examples/concepts/knowledge/filters/docx/filtering.md): Learn how to filter knowledge base searches using Docx documents with user-specific metadata.
- [Filtering on load with Docx](https://docs.agno.com/examples/concepts/knowledge/filters/docx/filtering_on_load.md): Learn how to filter knowledge base at load time using Docx documents with user-specific metadata.
- [Knowledge filtering using Traditional RAG](https://docs.agno.com/examples/concepts/knowledge/filters/filtering-traditional-RAG.md): Learn how to filter knowledge in Traditional RAG using metadata like user IDs, document types, and years. This example demonstrates how to set up a knowledge base with filters and query it effectively.
- [Filtering on ChromaDB](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_chroma_db.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in ChromaDB.
- [Filtering on LanceDB](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_lance_db.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in LanceDB.
- [Filtering on MilvusDB](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_milvus_db.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in MilvusDB.
- [Filtering on MongoDB](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_mongo_db.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in MongoDB.
- [Filtering on PgVector](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_pgvector.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in PgVector.
- [Filtering on Pinecone](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_pinecone.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in Pinecone.
- [Filtering on Qdrant](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_qdrant_db.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in Qdrant.
- [Filtering on Weaviate](https://docs.agno.com/examples/concepts/knowledge/filters/filtering_weaviate.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata in Weaviate.
- [Agentic filtering with Json](https://docs.agno.com/examples/concepts/knowledge/filters/json/agentic_filtering.md): Learn how to do agentic knowledge filtering using Json documents with user-specific metadata.
- [Filtering with Json](https://docs.agno.com/examples/concepts/knowledge/filters/json/filtering.md): Learn how to filter knowledge base searches using Json documents with user-specific metadata.
- [Filtering on load with Json](https://docs.agno.com/examples/concepts/knowledge/filters/json/filtering_on_load.md): Learn how to filter knowledge base at load time using Json documents with user-specific metadata.
- [Agentic filtering with Pdf](https://docs.agno.com/examples/concepts/knowledge/filters/pdf/agentic_filtering.md): Learn how to do agentic knowledge filtering using Pdf documents with user-specific metadata.
- [Filtering with Pdf](https://docs.agno.com/examples/concepts/knowledge/filters/pdf/filtering.md): Learn how to filter knowledge base searches using Pdf documents with user-specific metadata.
- [Filtering on load with Pdf](https://docs.agno.com/examples/concepts/knowledge/filters/pdf/filtering_on_load.md): Learn how to filter knowledge base at load time using Pdf documents with user-specific metadata.
- [Agentic filtering with Pdf-Url](https://docs.agno.com/examples/concepts/knowledge/filters/pdf_url/agentic_filtering.md): Learn how to do agentic knowledge filtering using Pdf-Url documents with user-specific metadata.
- [Filtering with Pdf-Url](https://docs.agno.com/examples/concepts/knowledge/filters/pdf_url/filtering.md): Learn how to filter knowledge base searches using Pdf-Url documents with user-specific metadata.
- [Filtering on load with Pdf-Url](https://docs.agno.com/examples/concepts/knowledge/filters/pdf_url/filtering_on_load.md): Learn how to filter knowledge base at load time using Pdf-Url documents with user-specific metadata.
- [Agentic filtering with Text](https://docs.agno.com/examples/concepts/knowledge/filters/text/agentic_filtering.md): Learn how to do agentic knowledge filtering using Text documents with user-specific metadata.
- [Filtering with Text](https://docs.agno.com/examples/concepts/knowledge/filters/text/filtering.md): Learn how to filter knowledge base searches using Text documents with user-specific metadata.
- [Filtering on load with Text](https://docs.agno.com/examples/concepts/knowledge/filters/text/filtering_on_load.md): Learn how to filter knowledge base at load time using Text documents with user-specific metadata.
- [Built-in Memory](https://docs.agno.com/examples/concepts/memory/00-built-in-memory.md)
- [Standalone Memory Operations](https://docs.agno.com/examples/concepts/memory/01-standalone-memory.md)
- [Persistent Memory with SQLite](https://docs.agno.com/examples/concepts/memory/02-persistent-memory.md)
- [Custom Memory Creation](https://docs.agno.com/examples/concepts/memory/03-custom-memory-creation.md)
- [Memory Search](https://docs.agno.com/examples/concepts/memory/04-memory-search.md)
- [Agent With Memory](https://docs.agno.com/examples/concepts/memory/05-agent-with-memory.md)
- [Agentic Memory](https://docs.agno.com/examples/concepts/memory/06-agentic-memory.md)
- [Agent with Session Summaries](https://docs.agno.com/examples/concepts/memory/07-agent-with-summaries.md)
- [Multiple Agents Sharing Memory](https://docs.agno.com/examples/concepts/memory/08-agents-share-memory.md)
- [Custom Memory](https://docs.agno.com/examples/concepts/memory/09-custom-memory.md)
- [Multi-User Multi-Session Chat](https://docs.agno.com/examples/concepts/memory/10-multi-user-multi-session-chat.md)
- [Multi-User Multi-Session Chat Concurrent](https://docs.agno.com/examples/concepts/memory/11-multi-user-multi-session-chat-concurrent.md)
- [Memory References](https://docs.agno.com/examples/concepts/memory/12-memory-references.md)
- [Session Summary References](https://docs.agno.com/examples/concepts/memory/13-session-summary-references.md)
- [MongoDB Memory Storage](https://docs.agno.com/examples/concepts/memory/db/mem-mongodb-memory.md)
- [PostgreSQL Memory Storage](https://docs.agno.com/examples/concepts/memory/db/mem-postgres-memory.md)
- [Redis Memory Storage](https://docs.agno.com/examples/concepts/memory/db/mem-redis-memory.md)
- [SQLite Memory Storage](https://docs.agno.com/examples/concepts/memory/db/mem-sqlite-memory.md)
- [Mem0 Memory](https://docs.agno.com/examples/concepts/memory/mem0-memory.md)
- [Audio Input Output](https://docs.agno.com/examples/concepts/multimodal/audio-input-output.md)
- [Multi-turn Audio Agent](https://docs.agno.com/examples/concepts/multimodal/audio-multi-turn.md)
- [Audio Sentiment Analysis Agent](https://docs.agno.com/examples/concepts/multimodal/audio-sentiment-analysis.md)
- [Audio Streaming Agent](https://docs.agno.com/examples/concepts/multimodal/audio-streaming.md)
- [Audio to text Agent](https://docs.agno.com/examples/concepts/multimodal/audio-to-text.md)
- [Blog to Podcast Agent](https://docs.agno.com/examples/concepts/multimodal/blog-to-podcast.md)
- [Generate Images with Intermediate Steps](https://docs.agno.com/examples/concepts/multimodal/generate-image.md)
- [Generate Music using Models Lab](https://docs.agno.com/examples/concepts/multimodal/generate-music-agent.md)
- [Generate Video using Models Lab](https://docs.agno.com/examples/concepts/multimodal/generate-video-models-lab.md)
- [Generate Video using Replicate](https://docs.agno.com/examples/concepts/multimodal/generate-video-replicate.md)
- [Image to Audio Agent](https://docs.agno.com/examples/concepts/multimodal/image-to-audio.md)
- [Image to Image Agent](https://docs.agno.com/examples/concepts/multimodal/image-to-image.md)
- [Image to Text Agent](https://docs.agno.com/examples/concepts/multimodal/image-to-text.md)
- [Video Caption Agent](https://docs.agno.com/examples/concepts/multimodal/video-caption.md)
- [Video to Shorts Agent](https://docs.agno.com/examples/concepts/multimodal/video-to-shorts.md)
- [Arize Phoenix via OpenInference](https://docs.agno.com/examples/concepts/observability/arize-phoenix-via-openinference.md)
- [Arize Phoenix via OpenInference (Local Collector)](https://docs.agno.com/examples/concepts/observability/arize-phoenix-via-openinference-local.md)
- [Langfuse via OpenInference](https://docs.agno.com/examples/concepts/observability/langfuse_via_openinference.md)
- [Langfuse via OpenLIT](https://docs.agno.com/examples/concepts/observability/langfuse_via_openlit.md)
- [LangSmith](https://docs.agno.com/examples/concepts/observability/langsmith-via-openinference.md)
- [Langtrace](https://docs.agno.com/examples/concepts/observability/langtrace-op.md)
- [Weave](https://docs.agno.com/examples/concepts/observability/weave-op.md)
- [Agent Extra Metrics](https://docs.agno.com/examples/concepts/others/agent_extra_metrics.md)
- [Agent Metrics](https://docs.agno.com/examples/concepts/others/agent_metrics.md)
- [Datetime Instructions](https://docs.agno.com/examples/concepts/others/datetime_instructions.md)
- [Image Input High Fidelity](https://docs.agno.com/examples/concepts/others/image_input_high_fidelity.md)
- [Input as Dictionary](https://docs.agno.com/examples/concepts/others/input_as_dict.md)
- [Input as List](https://docs.agno.com/examples/concepts/others/input_as_list.md)
- [Input as Message](https://docs.agno.com/examples/concepts/others/input_as_message.md)
- [Input as Messages List](https://docs.agno.com/examples/concepts/others/input_as_messages_list.md)
- [Instructions](https://docs.agno.com/examples/concepts/others/instructions.md)
- [Instructions via Function](https://docs.agno.com/examples/concepts/others/instructions_via_function.md)
- [Intermediate Steps](https://docs.agno.com/examples/concepts/others/intermediate_steps.md)
- [Location Instructions](https://docs.agno.com/examples/concepts/others/location_instructions.md): Add the current location to the instructions of an agent.
- [Response as Variable](https://docs.agno.com/examples/concepts/others/response_as_variable.md)
- [Success Criteria](https://docs.agno.com/examples/concepts/others/success_criteria.md)
- [Agentic RAG with Agent UI](https://docs.agno.com/examples/concepts/rag/agentic-rag-agent-ui.md)
- [Agentic RAG with LanceDB](https://docs.agno.com/examples/concepts/rag/agentic-rag-lancedb.md)
- [Agentic RAG with PgVector](https://docs.agno.com/examples/concepts/rag/agentic-rag-pgvector.md)
- [Agentic RAG with Reranking](https://docs.agno.com/examples/concepts/rag/agentic-rag-with-reranking.md)
- [RAG with LanceDB and SQLite](https://docs.agno.com/examples/concepts/rag/rag-with-lance-db-and-sqlite.md)
- [Traditional RAG with LanceDB](https://docs.agno.com/examples/concepts/rag/traditional-rag-lancedb.md)
- [Traditional RAG with PgVector](https://docs.agno.com/examples/concepts/rag/traditional-rag-pgvector.md)
- [Basic Reasoning Agent](https://docs.agno.com/examples/concepts/reasoning/agents/basic-cot.md)
- [Capture Reasoning Content](https://docs.agno.com/examples/concepts/reasoning/agents/capture-reasoning-content-cot.md)
- [Non-Reasoning Model Agent](https://docs.agno.com/examples/concepts/reasoning/agents/non-reasoning-model.md)
- [Azure AI Foundry](https://docs.agno.com/examples/concepts/reasoning/models/azure-ai-foundary/azure-ai-foundary.md)
- [Azure OpenAI o1](https://docs.agno.com/examples/concepts/reasoning/models/azure-openai/o1.md)
- [Azure OpenAI o3](https://docs.agno.com/examples/concepts/reasoning/models/azure-openai/o3-tools.md)
- [Azure OpenAI GPT 4.1](https://docs.agno.com/examples/concepts/reasoning/models/azure-openai/reasoning-model-gpt4-1.md)
- [DeepSeek Reasoner](https://docs.agno.com/examples/concepts/reasoning/models/deepseek/trolley-problem.md)
- [Groq DeepSeek R1](https://docs.agno.com/examples/concepts/reasoning/models/groq/groq-basic.md)
- [Groq Claude + DeepSeek R1](https://docs.agno.com/examples/concepts/reasoning/models/groq/groq-plus-claude.md)
- [Ollama DeepSeek R1](https://docs.agno.com/examples/concepts/reasoning/models/ollama/ollama-basic.md)
- [OpenAI o1 pro](https://docs.agno.com/examples/concepts/reasoning/models/openai/o1-pro.md)
- [OpenAI o3-mini](https://docs.agno.com/examples/concepts/reasoning/models/openai/o3-mini-tools.md)
- [OpenAI o3-mini with reasoning effort](https://docs.agno.com/examples/concepts/reasoning/models/openai/reasoning-effort.md)
- [xAI Grok 3 Mini](https://docs.agno.com/examples/concepts/reasoning/models/xai/reasoning-effort.md)
- [Team with Knowledge Tools](https://docs.agno.com/examples/concepts/reasoning/teams/knowledge-tool-team.md)
- [Team with Reasoning Tools](https://docs.agno.com/examples/concepts/reasoning/teams/reasoning-tool-team.md)
- [Gemini with Reasoning Tools](https://docs.agno.com/examples/concepts/reasoning/tools/gemini-reasoning-tools.md)
- [Gemini with Thinking Tools](https://docs.agno.com/examples/concepts/reasoning/tools/gemini-thinking-tools.md)
- [Reasoning Agent with Knowledge Tools](https://docs.agno.com/examples/concepts/reasoning/tools/knowledge-tools.md)
- [Reasoning Agent with Reasoning Tools](https://docs.agno.com/examples/concepts/reasoning/tools/reasoning-tools.md)
- [Reasoning Agent with Thinking Tools](https://docs.agno.com/examples/concepts/reasoning/tools/thinking-tools.md)
- [Basic State Management](https://docs.agno.com/examples/concepts/state/01-session-state.md)
- [State in Instructions](https://docs.agno.com/examples/concepts/state/02-state-in-prompt.md)
- [Persistant State with Storage](https://docs.agno.com/examples/concepts/state/03-session-state-storage.md)
- [Multi User State](https://docs.agno.com/examples/concepts/state/04-session-state-user-id.md)
- [State Management Across Multiple Runs](https://docs.agno.com/examples/concepts/state/05-session-state-full-example.md)
- [Team with Shared State](https://docs.agno.com/examples/concepts/state/06-team-session-state.md)
- [DynamoDB Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/dynamodb.md)
- [JSON Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/json.md)
- [Mongo Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/mongodb.md)
- [MySQL Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/mysql.md)
- [Postgres Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/postgres.md)
- [Redis Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/redis.md)
- [Singlestore Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/singlestore.md)
- [Sqlite Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/sqlite.md)
- [YAML Agent Storage](https://docs.agno.com/examples/concepts/storage/agent_storage/yaml.md)
- [DynamoDB Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/dynamodb.md)
- [JSON Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/json.md)
- [Mongo Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/mongodb.md)
- [MySQL Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/mysql.md)
- [Postgres Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/postgres.md)
- [Redis Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/redis.md)
- [Singlestore Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/singlestore.md)
- [Sqlite Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/sqlite.md)
- [YAML Team Storage](https://docs.agno.com/examples/concepts/storage/team_storage/yaml.md)
- [DynamoDB Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/dynamodb.md)
- [JSON Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/json.md)
- [MongoDB Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/mongodb.md)
- [MySQL Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/mysql.md)
- [Postgres Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/postgres.md)
- [Redis Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/redis.md)
- [Singlestore Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/singlestore.md)
- [SQLite Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/sqlite.md)
- [YAML Workflow Storage](https://docs.agno.com/examples/concepts/storage/workflow_storage/yaml.md)
- [CSV Tools](https://docs.agno.com/examples/concepts/tools/database/csv.md)
- [DuckDB Tools](https://docs.agno.com/examples/concepts/tools/database/duckdb.md)
- [null](https://docs.agno.com/examples/concepts/tools/database/pandas.md)
- [Postgres Tools](https://docs.agno.com/examples/concepts/tools/database/postgres.md)
- [SQL Tools](https://docs.agno.com/examples/concepts/tools/database/sql.md)
- [Zep Memory Tools](https://docs.agno.com/examples/concepts/tools/database/zep.md)
- [Zep Async Memory Tools](https://docs.agno.com/examples/concepts/tools/database/zep_async.md)
- [Calculator](https://docs.agno.com/examples/concepts/tools/local/calculator.md)
- [Docker Tools](https://docs.agno.com/examples/concepts/tools/local/docker.md)
- [File Tools](https://docs.agno.com/examples/concepts/tools/local/file.md)
- [Python Tools](https://docs.agno.com/examples/concepts/tools/local/python.md)
- [Shell Tools](https://docs.agno.com/examples/concepts/tools/local/shell.md)
- [Sleep Tools](https://docs.agno.com/examples/concepts/tools/local/sleep.md)
- [Airbnb MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/airbnb.md)
- [GitHub MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/github.md)
- [Notion MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/notion.md)
- [Pipedream Auth](https://docs.agno.com/examples/concepts/tools/mcp/pipedream_auth.md): This example shows how to add authorization when integrating Pipedream MCP servers with Agno Agents.
- [Pipedream Google Calendar](https://docs.agno.com/examples/concepts/tools/mcp/pipedream_google_calendar.md): This example shows how to use the Google Calendar Pipedream MCP server with Agno Agents.
- [Pipedream LinkedIn](https://docs.agno.com/examples/concepts/tools/mcp/pipedream_linkedin.md): This example shows how to use the LinkedIn Pipedream MCP server with Agno Agents.
- [Pipedream Slack](https://docs.agno.com/examples/concepts/tools/mcp/pipedream_slack.md): This example shows how to use the Slack Pipedream MCP server with Agno Agents.
- [Stagehand MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/stagehand.md): A web scraping agent that uses the Stagehand MCP server to automate browser interactions and create a structured content digest from Hacker News.
- [Stripe MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/stripe.md)
- [Supabase MCP agent](https://docs.agno.com/examples/concepts/tools/mcp/supabase.md)
- [Meeting Summary Agent](https://docs.agno.com/examples/concepts/tools/models/openai/meeting-summarizer.md): Multi-modal Agno agent that transcribes meeting recordings, extracts key insights, generates visual summaries, and creates audio summaries using OpenAI tools.
- [Recipe RAG Image Agent](https://docs.agno.com/examples/concepts/tools/models/openai/rag-recipe-image.md)
- [Airflow Tools](https://docs.agno.com/examples/concepts/tools/others/airflow.md)
- [Apify Tools](https://docs.agno.com/examples/concepts/tools/others/apify.md)
- [AWS Lambda Tools](https://docs.agno.com/examples/concepts/tools/others/aws_lambda.md)
- [AWS SES Tools](https://docs.agno.com/examples/concepts/tools/others/aws_ses.md)
- [Cal.com Tools](https://docs.agno.com/examples/concepts/tools/others/calcom.md)
- [Composio Tools](https://docs.agno.com/examples/concepts/tools/others/composio.md)
- [Confluence Tools](https://docs.agno.com/examples/concepts/tools/others/confluence.md)
- [DALL-E Tools](https://docs.agno.com/examples/concepts/tools/others/dalle.md)
- [Daytona Code Execution](https://docs.agno.com/examples/concepts/tools/others/daytona.md): Learn to use Agno's Daytona integration to run your Agent-generated code in a secure sandbox.
- [Desi Vocal Tools](https://docs.agno.com/examples/concepts/tools/others/desi_vocal.md)
- [E2B Code Execution](https://docs.agno.com/examples/concepts/tools/others/e2b.md): Learn to use Agno's E2B integration to run your Agent-generated code in a secure sandbox.
- [Fal Tools](https://docs.agno.com/examples/concepts/tools/others/fal.md)
- [Financial Datasets Tools](https://docs.agno.com/examples/concepts/tools/others/financial_datasets.md)
- [Giphy Tools](https://docs.agno.com/examples/concepts/tools/others/giphy.md)
- [GitHub Tools](https://docs.agno.com/examples/concepts/tools/others/github.md)
- [Google Calendar Tools](https://docs.agno.com/examples/concepts/tools/others/google_calendar.md)
- [Google Maps Tools](https://docs.agno.com/examples/concepts/tools/others/google_maps.md)
- [Jira Tools](https://docs.agno.com/examples/concepts/tools/others/jira.md)
- [Linear Tools](https://docs.agno.com/examples/concepts/tools/others/linear.md)
- [Luma Labs Tools](https://docs.agno.com/examples/concepts/tools/others/lumalabs.md)
- [MLX Transcribe Tools](https://docs.agno.com/examples/concepts/tools/others/mlx_transcribe.md)
- [Models Labs Tools](https://docs.agno.com/examples/concepts/tools/others/models_labs.md)
- [OpenBB Tools](https://docs.agno.com/examples/concepts/tools/others/openbb.md)
- [Replicate Tools](https://docs.agno.com/examples/concepts/tools/others/replicate.md)
- [Resend Tools](https://docs.agno.com/examples/concepts/tools/others/resend.md)
- [Todoist Tools](https://docs.agno.com/examples/concepts/tools/others/todoist.md)
- [YFinance Tools](https://docs.agno.com/examples/concepts/tools/others/yfinance.md)
- [YouTube Tools](https://docs.agno.com/examples/concepts/tools/others/youtube.md)
- [Zendesk Tools](https://docs.agno.com/examples/concepts/tools/others/zendesk.md)
- [ArXiv Tools](https://docs.agno.com/examples/concepts/tools/search/arxiv.md)
- [Baidu Search Tools](https://docs.agno.com/examples/concepts/tools/search/baidusearch.md)
- [Brave Search Tools](https://docs.agno.com/examples/concepts/tools/search/bravesearch.md)
- [Crawl4ai Tools](https://docs.agno.com/examples/concepts/tools/search/crawl4ai.md)
- [DuckDuckGo Search](https://docs.agno.com/examples/concepts/tools/search/duckduckgo.md)
- [Exa Tools](https://docs.agno.com/examples/concepts/tools/search/exa.md)
- [Google Search Tools](https://docs.agno.com/examples/concepts/tools/search/google_search.md)
- [Hacker News Tools](https://docs.agno.com/examples/concepts/tools/search/hackernews.md)
- [PubMed Tools](https://docs.agno.com/examples/concepts/tools/search/pubmed.md)
- [SearxNG Tools](https://docs.agno.com/examples/concepts/tools/search/searxng.md)
- [SerpAPI Tools](https://docs.agno.com/examples/concepts/tools/search/serpapi.md)
- [Tavily Tools](https://docs.agno.com/examples/concepts/tools/search/tavily.md)
- [Wikipedia Tools](https://docs.agno.com/examples/concepts/tools/search/wikipedia.md)
- [Discord Tools](https://docs.agno.com/examples/concepts/tools/social/discord.md)
- [Email Tools](https://docs.agno.com/examples/concepts/tools/social/email.md)
- [Slack Tools](https://docs.agno.com/examples/concepts/tools/social/slack.md)
- [Twilio Tools](https://docs.agno.com/examples/concepts/tools/social/twilio.md)
- [Webex Tools](https://docs.agno.com/examples/concepts/tools/social/webex.md)
- [WhatsApp Tools](https://docs.agno.com/examples/concepts/tools/social/whatsapp.md)
- [X (Twitter) Tools](https://docs.agno.com/examples/concepts/tools/social/x.md)
- [BrightData Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/brightdata.md)
- [Firecrawl Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/firecrawl.md): Use Firecrawl with Agno to scrape and crawl the web.
- [Jina Reader Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/jina_reader.md)
- [Newspaper Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/newspaper.md)
- [Newspaper4k Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/newspaper4k.md)
- [Oxylabs Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/oxylabs.md): Use Oxylabs with Agno to scrape and crawl the web.
- [Spider Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/spider.md)
- [Website Tools](https://docs.agno.com/examples/concepts/tools/web_scrape/website.md)
- [User Confirmation Required](https://docs.agno.com/examples/concepts/user-control-flows/01-confirmation-required.md): This example demonstrates how to implement human-in-the-loop functionality by requiring user confirmation before executing tool calls.
- [Async User Confirmation](https://docs.agno.com/examples/concepts/user-control-flows/02-confirmation-required-async.md): This example demonstrates how to implement asynchronous user confirmation flows, allowing for non-blocking execution while waiting for user input.
- [Streaming User Confirmation](https://docs.agno.com/examples/concepts/user-control-flows/03-confirmation-required-stream.md): This example demonstrates how to implement streaming user confirmation flows, allowing for real-time interaction and response streaming.
- [User Input Required](https://docs.agno.com/examples/concepts/user-control-flows/04-user-input-required.md): This example demonstrates how to implement user input collection during agent execution, allowing users to provide specific information for tool parameters.
- [Async User Input](https://docs.agno.com/examples/concepts/user-control-flows/05-user-input-required-async.md): This example demonstrates how to implement asynchronous user input collection, allowing for non-blocking execution while gathering user information.
- [Streaming User Input](https://docs.agno.com/examples/concepts/user-control-flows/06-user-input-required-stream.md): This example demonstrates how to implement streaming user input collection, allowing for real-time interaction and response streaming while gathering user information.
- [Dynamic User Input (Agentic)](https://docs.agno.com/examples/concepts/user-control-flows/07-agentic-user-input.md): This example demonstrates how to implement dynamic user input collection using the `UserControlFlowTools`, allowing the agent to request information as needed during execution.
- [External Tool Execution](https://docs.agno.com/examples/concepts/user-control-flows/08-external-tool-execution.md): This example demonstrates how to execute tool calls outside of the agent's control, allowing for custom execution logic and security measures.
- [Async External Tool Execution](https://docs.agno.com/examples/concepts/user-control-flows/09-external-tool-execution-async.md): This example demonstrates how to implement asynchronous external tool execution, allowing for non-blocking execution of tools outside of the agent's control.
- [Streaming External Tool Execution](https://docs.agno.com/examples/concepts/user-control-flows/10-external-tool-execution-stream.md): This example demonstrates how to implement streaming external tool execution, allowing for real-time interaction and response streaming while executing tools outside of the agent's control.
- [Azure Cosmos DB MongoDB vCore Integration](https://docs.agno.com/examples/concepts/vectordb/azure_cosmos_mongodb.md)
- [Cassandra Integration](https://docs.agno.com/examples/concepts/vectordb/cassandra.md)
- [ChromaDB Integration](https://docs.agno.com/examples/concepts/vectordb/chromadb.md)
- [Clickhouse Integration](https://docs.agno.com/examples/concepts/vectordb/clickhouse.md)
- [Couchbase Integration](https://docs.agno.com/examples/concepts/vectordb/couchbase.md)
- [LanceDB Integration](https://docs.agno.com/examples/concepts/vectordb/lancedb.md)
- [Milvus Integration](https://docs.agno.com/examples/concepts/vectordb/milvus.md)
- [MongoDB Integration](https://docs.agno.com/examples/concepts/vectordb/mongodb.md)
- [PgVector Integration](https://docs.agno.com/examples/concepts/vectordb/pgvector.md)
- [Pinecone Integration](https://docs.agno.com/examples/concepts/vectordb/pinecone.md)
- [Qdrant Integration](https://docs.agno.com/examples/concepts/vectordb/qdrant.md)
- [SingleStore Integration](https://docs.agno.com/examples/concepts/vectordb/singlestore.md)
- [Weaviate Integration](https://docs.agno.com/examples/concepts/vectordb/weaviate.md)
- [Accuracy with Given Answer](https://docs.agno.com/examples/evals/accuracy/accuracy_with_given_answer.md): Learn how to evaluate the accuracy of an Agno Agent's response with a given answer.
- [Accuracy with Teams](https://docs.agno.com/examples/evals/accuracy/accuracy_with_teams.md): Learn how to evaluate the accuracy of an Agno Team.
- [Accuracy with Tools](https://docs.agno.com/examples/evals/accuracy/accuracy_with_tools.md): Learn how to evaluate the accuracy of an Agent that is using tools.
- [Simple Accuracy](https://docs.agno.com/examples/evals/accuracy/basic.md): Learn to check how complete, correct and accurate an Agno Agent's response is.
- [Performance on Agent Instantiation](https://docs.agno.com/examples/evals/performance/performance_agent_instantiation.md): Evaluation to analyze the runtime and memory usage of an Agent.
- [Performance on Agent Instantiation with Tool](https://docs.agno.com/examples/evals/performance/performance_instantiation_with_tool.md): Example showing how to analyze the runtime and memory usage of an Agent that is using tools.
- [Performance on Agent Response](https://docs.agno.com/examples/evals/performance/performance_simple_response.md): Example showing how to analyze the runtime and memory usage of an Agent's run, given its response.
- [Performance with Teams](https://docs.agno.com/examples/evals/performance/performance_team_instantiation.md): Learn how to analyze the runtime and memory usage of an Agno Team.
- [Performance on Agent with Storage](https://docs.agno.com/examples/evals/performance/performance_with_storage.md): Example showing how to analyze the runtime and memory usage of an Agent that is using storage.
- [Reliability with Single Tool](https://docs.agno.com/examples/evals/reliability/basic.md): Evaluation to assert an Agent is making the expected tool calls.
- [Reliability with Multiple Tools](https://docs.agno.com/examples/evals/reliability/reliability_with_multiple_tools.md): Learn how to assert an Agno Agent is making multiple expected tool calls.
- [Reliability with Teams](https://docs.agno.com/examples/evals/reliability/reliability_with_teams.md): Learn how to assert an Agno Team is making the expected tool calls.
- [Agent Context](https://docs.agno.com/examples/getting-started/agent-context.md)
- [Agent Session](https://docs.agno.com/examples/getting-started/agent-session.md)
- [Agent State](https://docs.agno.com/examples/getting-started/agent-state.md)
- [Agent Team](https://docs.agno.com/examples/getting-started/agent-team.md)
- [Agent with Knowledge](https://docs.agno.com/examples/getting-started/agent-with-knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/getting-started/agent-with-storage.md)
- [Agent with Tools](https://docs.agno.com/examples/getting-started/agent-with-tools.md)
- [Audio Agent](https://docs.agno.com/examples/getting-started/audio-agent.md)
- [Basic Agent](https://docs.agno.com/examples/getting-started/basic-agent.md)
- [Custom Tools](https://docs.agno.com/examples/getting-started/custom-tools.md)
- [Human in the Loop](https://docs.agno.com/examples/getting-started/human-in-the-loop.md)
- [Image Agent](https://docs.agno.com/examples/getting-started/image-agent.md)
- [Image Generation](https://docs.agno.com/examples/getting-started/image-generation.md)
- [Introduction](https://docs.agno.com/examples/getting-started/introduction.md)
- [Research Agent](https://docs.agno.com/examples/getting-started/research-agent.md)
- [Research Workflow](https://docs.agno.com/examples/getting-started/research-workflow.md)
- [Retry Functions](https://docs.agno.com/examples/getting-started/retry-functions.md)
- [Structured Output](https://docs.agno.com/examples/getting-started/structured-output.md)
- [User Memories](https://docs.agno.com/examples/getting-started/user-memories.md)
- [Video Generation](https://docs.agno.com/examples/getting-started/video-generation.md)
- [Examples Gallery](https://docs.agno.com/examples/introduction.md): Explore Agno's example gallery showcasing everything from single-agent tasks to sophisticated multi-agent workflows.
- [Basic Agent](https://docs.agno.com/examples/models/anthropic/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/anthropic/basic_stream.md)
- [Code Execution Tool](https://docs.agno.com/examples/models/anthropic/code_execution.md): Learn how to use Anthropic's code execution tool with Agno.
- [File Upload](https://docs.agno.com/examples/models/anthropic/file_upload.md): Learn how to use Anthropic's Files API with Agno.
- [Image Input Bytes Content](https://docs.agno.com/examples/models/anthropic/image_input_bytes.md)
- [Image Input URL](https://docs.agno.com/examples/models/anthropic/image_input_url.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/anthropic/knowledge.md)
- [PDF Input Bytes Agent](https://docs.agno.com/examples/models/anthropic/pdf_input_bytes.md)
- [PDF Input Local Agent](https://docs.agno.com/examples/models/anthropic/pdf_input_local.md)
- [PDF Input URL Agent](https://docs.agno.com/examples/models/anthropic/pdf_input_url.md)
- [Prompt Caching](https://docs.agno.com/examples/models/anthropic/prompt_caching.md): Learn how to use prompt caching with Anthropic models and Agno.
- [Agent with Storage](https://docs.agno.com/examples/models/anthropic/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/anthropic/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/anthropic/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/aws/bedrock/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/aws/bedrock/basic_stream.md)
- [Agent with Image Input](https://docs.agno.com/examples/models/aws/bedrock/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/aws/bedrock/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/aws/bedrock/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/aws/bedrock/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/aws/bedrock/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/aws/claude/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/aws/claude/basic_stream.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/aws/claude/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/aws/claude/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/aws/claude/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/aws/claude/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/azure/ai_foundry/basic.md)
- [Basic Streaming](https://docs.agno.com/examples/models/azure/ai_foundry/basic_stream.md)
- [Agent with Knowledge Base](https://docs.agno.com/examples/models/azure/ai_foundry/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/azure/ai_foundry/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/azure/ai_foundry/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/azure/ai_foundry/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/azure/openai/basic.md)
- [Basic Streaming](https://docs.agno.com/examples/models/azure/openai/basic_stream.md)
- [Agent with Knowledge Base](https://docs.agno.com/examples/models/azure/openai/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/azure/openai/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/azure/openai/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/azure/openai/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/cerebras/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/cerebras/basic_stream.md)
- [Agent with Knowledge Base](https://docs.agno.com/examples/models/cerebras/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/cerebras/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/cerebras/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/cerebras/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/cerebras_openai/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/cerebras_openai/basic_stream.md)
- [Agent with Tools](https://docs.agno.com/examples/models/cerebras_openai/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/cohere/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/cohere/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/cohere/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/cohere/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/cohere/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/cohere/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/cohere/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/deepinfra/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/deepinfra/basic_stream.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/deepinfra/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/deepinfra/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/deepseek/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/deepseek/basic_stream.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/deepseek/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/deepseek/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/fireworks/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/fireworks/basic_stream.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/fireworks/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/fireworks/tool_use.md)
- [Audio Input (Bytes Content)](https://docs.agno.com/examples/models/gemini/audio_input_bytes_content.md)
- [Audio Input (Upload the file)](https://docs.agno.com/examples/models/gemini/audio_input_file_upload.md)
- [Audio Input (Local file)](https://docs.agno.com/examples/models/gemini/audio_input_local_file_upload.md)
- [Basic Agent](https://docs.agno.com/examples/models/gemini/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/gemini/basic_stream.md)
- [Flash Thinking Agent](https://docs.agno.com/examples/models/gemini/flash_thinking.md)
- [Image Agent](https://docs.agno.com/examples/models/gemini/image_input.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/gemini/knowledge.md)
- [Agent with PDF Input (Local file)](https://docs.agno.com/examples/models/gemini/pdf_input_local.md)
- [Agent with PDF Input (URL)](https://docs.agno.com/examples/models/gemini/pdf_input_url.md)
- [Agent with Storage](https://docs.agno.com/examples/models/gemini/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/gemini/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/gemini/tool_use.md)
- [Video Input (Bytes Content)](https://docs.agno.com/examples/models/gemini/video_input_bytes_content.md)
- [Video Input (File Upload)](https://docs.agno.com/examples/models/gemini/video_input_file_upload.md)
- [Video Input (Local File Upload)](https://docs.agno.com/examples/models/gemini/video_input_local_file_upload.md)
- [Basic Agent](https://docs.agno.com/examples/models/groq/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/groq/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/groq/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/groq/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/groq/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/groq/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/groq/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/huggingface/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/huggingface/basic_stream.md)
- [Llama Essay Writer](https://docs.agno.com/examples/models/huggingface/llama_essay_writer.md)
- [Async Basic Agent](https://docs.agno.com/examples/models/ibm/async_basic.md)
- [Async Streaming Agent](https://docs.agno.com/examples/models/ibm/async_basic_stream.md)
- [Agent with Async Tool Usage](https://docs.agno.com/examples/models/ibm/async_tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/ibm/basic.md)
- [Streaming Basic Agent](https://docs.agno.com/examples/models/ibm/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/ibm/image_agent_bytes.md)
- [RAG Agent](https://docs.agno.com/examples/models/ibm/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/ibm/storage.md)
- [Agent with Structured Output](https://docs.agno.com/examples/models/ibm/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/ibm/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/litellm/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/litellm/basic_stream.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/litellm/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/litellm/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/litellm/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/litellm/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/litellm_openai/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/litellm_openai/basic_stream.md)
- [Agent with Tools](https://docs.agno.com/examples/models/litellm_openai/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/lmstudio/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/lmstudio/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/lmstudio/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/lmstudio/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/lmstudio/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/lmstudio/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/lmstudio/tool_use.md)
- [null](https://docs.agno.com/examples/models/meta/async_basic.md)
- [null](https://docs.agno.com/examples/models/meta/async_image_input.md)
- [null](https://docs.agno.com/examples/models/meta/async_stream.md)
- [null](https://docs.agno.com/examples/models/meta/async_structured_output.md)
- [null](https://docs.agno.com/examples/models/meta/async_tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/meta/basic.md)
- [null](https://docs.agno.com/examples/models/meta/basic_stream.md)
- [null](https://docs.agno.com/examples/models/meta/image_input.md)
- [null](https://docs.agno.com/examples/models/meta/structured_output.md)
- [null](https://docs.agno.com/examples/models/meta/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/mistral/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/mistral/basic_stream.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/mistral/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/mistral/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/nebius/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/nebius/basic_stream.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/nebius/knowledge.md)
- [Agent with Storage](https://docs.agno.com/examples/models/nebius/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/nebius/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/nebius/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/nvidia/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/nvidia/basic_stream.md)
- [Agent with Tools](https://docs.agno.com/examples/models/nvidia/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/ollama/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/ollama/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/ollama/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/ollama/knowledge.md)
- [Set Ollama Client](https://docs.agno.com/examples/models/ollama/set_client.md)
- [Agent with Storage](https://docs.agno.com/examples/models/ollama/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/ollama/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/ollama/tool_use.md)
- [Audio Input Agent](https://docs.agno.com/examples/models/openai/chat/audio_input_agent.md)
- [Audio Output Agent](https://docs.agno.com/examples/models/openai/chat/audio_output_agent.md)
- [Basic Agent](https://docs.agno.com/examples/models/openai/chat/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/openai/chat/basic_stream.md)
- [Generate Images](https://docs.agno.com/examples/models/openai/chat/generate_images.md)
- [Image Agent](https://docs.agno.com/examples/models/openai/chat/image_agent.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/openai/chat/knowledge.md)
- [Agent with Reasoning Effort](https://docs.agno.com/examples/models/openai/chat/reasoning_effort.md)
- [Agent with Storage](https://docs.agno.com/examples/models/openai/chat/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/openai/chat/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/openai/chat/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/openai/responses/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/openai/responses/basic_stream.md)
- [Image Agent](https://docs.agno.com/examples/models/openai/responses/image_agent.md)
- [Image Agent (Bytes Content)](https://docs.agno.com/examples/models/openai/responses/image_agent_bytes.md)
- [Agent with Knowledge](https://docs.agno.com/examples/models/openai/responses/knowledge.md)
- [Agent with PDF Input (Local File)](https://docs.agno.com/examples/models/openai/responses/pdf_input_local.md)
- [Agent with PDF Input (URL)](https://docs.agno.com/examples/models/openai/responses/pdf_input_url.md)
- [Agent with Storage](https://docs.agno.com/examples/models/openai/responses/storage.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/openai/responses/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/openai/responses/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/perplexity/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/perplexity/basic_stream.md)
- [Basic Agent](https://docs.agno.com/examples/models/together/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/together/basic_stream.md)
- [Agent with Structured Outputs](https://docs.agno.com/examples/models/together/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/together/tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/vercel/basic.md)
- [Streaming Agent](https://docs.agno.com/examples/models/vercel/basic_stream.md)
- [Agent with Tools](https://docs.agno.com/examples/models/vercel/tool_use.md)
- [Async Agent](https://docs.agno.com/examples/models/vllm/async_basic.md)
- [Async Agent with Streaming](https://docs.agno.com/examples/models/vllm/async_basic_stream.md)
- [Async Agent with Tools](https://docs.agno.com/examples/models/vllm/async_tool_use.md)
- [Agent with Streaming](https://docs.agno.com/examples/models/vllm/basic_stream.md)
- [Code Generation](https://docs.agno.com/examples/models/vllm/code_generation.md)
- [Agent with Memory](https://docs.agno.com/examples/models/vllm/memory.md)
- [Agent with Storage](https://docs.agno.com/examples/models/vllm/storage.md)
- [Structured Output](https://docs.agno.com/examples/models/vllm/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/vllm/tool_use.md)
- [Async Agent with Tools](https://docs.agno.com/examples/models/xai/async_tool_use.md)
- [Basic Agent](https://docs.agno.com/examples/models/xai/basic.md)
- [Async Basic Agent](https://docs.agno.com/examples/models/xai/basic_async.md)
- [Async Streaming Agent](https://docs.agno.com/examples/models/xai/basic_async_stream.md)
- [Streaming Agent](https://docs.agno.com/examples/models/xai/basic_stream.md)
- [Finance Agent](https://docs.agno.com/examples/models/xai/finance_agent.md)
- [Image Agent](https://docs.agno.com/examples/models/xai/image_agent.md)
- [Image Agent with Bytes](https://docs.agno.com/examples/models/xai/image_agent_bytes.md)
- [Image Agent with Memory](https://docs.agno.com/examples/models/xai/image_agent_with_memory.md)
- [Live Search Agent](https://docs.agno.com/examples/models/xai/live_search_agent.md)
- [Live Search Streaming Agent](https://docs.agno.com/examples/models/xai/live_search_agent_stream.md)
- [Reasoning Agent](https://docs.agno.com/examples/models/xai/reasoning_agent.md)
- [Structured Output Agent](https://docs.agno.com/examples/models/xai/structured_output.md)
- [Agent with Tools](https://docs.agno.com/examples/models/xai/tool_use.md)
- [Agentic RAG](https://docs.agno.com/examples/streamlit/agentic-rag.md)
- [Sage: Answer Engine](https://docs.agno.com/examples/streamlit/answer-engine.md)
- [Chess Battle](https://docs.agno.com/examples/streamlit/chess-team.md)
- [Game Generator](https://docs.agno.com/examples/streamlit/game-generator.md)
- [GeoBuddy](https://docs.agno.com/examples/streamlit/geobuddy.md)
- [SQL Agent](https://docs.agno.com/examples/streamlit/text-to-sql.md)
- [Discussion Team](https://docs.agno.com/examples/teams/collaborate/discussion_team.md)
- [Autonomous Startup Team](https://docs.agno.com/examples/teams/coordinate/autonomous_startup_team.md)
- [HackerNews Team](https://docs.agno.com/examples/teams/coordinate/hackernews_team.md)
- [News Agency Team](https://docs.agno.com/examples/teams/coordinate/news_agency_team.md)
- [AI Support Team](https://docs.agno.com/examples/teams/route/ai_support_team.md)
- [Multi Language Team](https://docs.agno.com/examples/teams/route/multi_language_team.md)
- [Team Session State](https://docs.agno.com/examples/teams/shared_state/team_session_state.md)
- [Scenario Testing](https://docs.agno.com/examples/testing/scenario/basic.md)
- [Async Hacker News Reporter](https://docs.agno.com/examples/workflows/async-hackernews-reporter.md): An asynchronous Hacker News reporter using workflows that fetches the latest news
- [Blog Post Generator](https://docs.agno.com/examples/workflows/blog-post-generator.md)
- [Content Creator](https://docs.agno.com/examples/workflows/content-creator.md)
- [Investment Report Generator](https://docs.agno.com/examples/workflows/investment-report-generator.md)
- [Personalized Email Generator](https://docs.agno.com/examples/workflows/personalized-email-generator.md)
- [Product Manager](https://docs.agno.com/examples/workflows/product-manager.md)
- [Startup Idea Validator](https://docs.agno.com/examples/workflows/startup-idea-validator.md)
- [Team Workflow](https://docs.agno.com/examples/workflows/team-workflow.md)
- [When to use a Workflow vs a Team in Agno](https://docs.agno.com/faq/When-to-use-a-Workflow-vs-a-Team-in-Agno.md)
- [Command line authentication](https://docs.agno.com/faq/cli-auth.md)
- [Connecting to Tableplus](https://docs.agno.com/faq/connecting-to-tableplus.md)
- [Could Not Connect To Docker](https://docs.agno.com/faq/could-not-connect-to-docker.md)
- [Setting Environment Variables](https://docs.agno.com/faq/environment-variables.md)
- [Memory V2](https://docs.agno.com/faq/memoryv2.md)
- [OpenAI Key Request While Using Other Models](https://docs.agno.com/faq/openai-key-request-for-other-models.md)
- [Playground Connection Issues](https://docs.agno.com/faq/playground-connection.md)
- [Structured outputs](https://docs.agno.com/faq/structured-outputs.md)
- [Tokens-per-minute rate limiting](https://docs.agno.com/faq/tpm-issues.md)
- [null](https://docs.agno.com/filters/agentic-filters.md)
- [null](https://docs.agno.com/filters/introduction.md)
- [null](https://docs.agno.com/filters/manual-filters.md)
- [Authenticate with Agno Platform](https://docs.agno.com/how-to/authentication.md): Set up authentication to start monitoring, tracking performance metrics, and managing your Agno workspace.
- [Contributing to Agno](https://docs.agno.com/how-to/contribute.md): Learn how to contribute to Agno through our fork and pull request workflow.
- [Install & Setup](https://docs.agno.com/how-to/install.md)
- [Migrate from Phidata to Agno](https://docs.agno.com/how-to/phidata-to-agno.md)
- [What is Agno?](https://docs.agno.com/introduction.md): Agno is a python framework for building multi-agent systems with shared memory, knowledge and reasoning.
- [What are Agents?](https://docs.agno.com/introduction/agents.md): **Agents are AI programs that operate autonomously.**
- [Community & Support](https://docs.agno.com/introduction/community.md): Join the Agno community to connect with builders, get support, and explore AI development opportunities.
- [Monitoring & Debugging](https://docs.agno.com/introduction/monitoring.md): Monitor your Agents, Teams and Workflows in real-time.
- [Multi Agent Systems](https://docs.agno.com/introduction/multi-agent-systems.md): Teams of Agents working together towards a common goal.
- [Playground](https://docs.agno.com/introduction/playground.md): **Agno provides an intuitive interface for testing and interacting with your AI agents.**
- [ArXiv Knowledge Base](https://docs.agno.com/knowledge/arxiv.md): Learn how to use ArXiv articles in your knowledge base.
- [Combined Knowledge Base](https://docs.agno.com/knowledge/combined.md): Learn how to combine multiple knowledge bases into one.
- [CSV Knowledge Base](https://docs.agno.com/knowledge/csv.md): Learn how to use local CSV files in your knowledge base.
- [CSV URL Knowledge Base](https://docs.agno.com/knowledge/csv-url.md): Learn how to use remote CSV files in your knowledge base.
- [Implementing a Custom Retriever](https://docs.agno.com/knowledge/custom_retriever.md): Learn how to implement a custom retriever for precise control over document retrieval in your knowledge base.
- [Document Knowledge Base](https://docs.agno.com/knowledge/document.md): Learn how to use local documents in your knowledge base.
- [Docx Knowledge Base](https://docs.agno.com/knowledge/docx.md): Learn how to use local docx files in your knowledge base.
- [Hybrid Search- Combining Keyword and Vector Search](https://docs.agno.com/knowledge/hybrid_search.md): Understanding Hybrid Search and its benefits in combining keyword and vector search for better results.
- [What is Knowledge?](https://docs.agno.com/knowledge/introduction.md): Knowledge is domain-specific information that the Agent can search at runtime to make better decisions (dynamic few-shot learning) and provide accurate responses (agentic RAG).
- [JSON Knowledge Base](https://docs.agno.com/knowledge/json.md): Learn how to use local JSON files in your knowledge base.
- [LangChain Knowledge Base](https://docs.agno.com/knowledge/langchain.md): Learn how to use a LangChain retriever or vector store as a knowledge base.
- [LightRAG Knowledge Base](https://docs.agno.com/knowledge/lightrag.md): Learn how to use LightRAG, a fast graph-based retrieval-augmented generation system for enhanced knowledge querying.
- [LlamaIndex Knowledge Base](https://docs.agno.com/knowledge/llamaindex.md): Learn how to use a LlamaIndex retriever or vector store as a knowledge base.
- [Markdown Knowledge Base](https://docs.agno.com/knowledge/markdown.md): Learn how to use Markdown files in your knowledge base.
- [PDF Knowledge Base](https://docs.agno.com/knowledge/pdf.md): Learn how to use local PDF files in your knowledge base.
- [PDF Bytes Knowledge Base](https://docs.agno.com/knowledge/pdf-bytes.md): Learn how to use in-memory PDF bytes in your knowledge base.
- [PDF URL Knowledge Base](https://docs.agno.com/knowledge/pdf-url.md): Learn how to use remote PDFs in your knowledge base.
- [S3 PDF Knowledge Base](https://docs.agno.com/knowledge/s3_pdf.md): Learn how to use PDFs from an S3 bucket in your knowledge base.
- [S3 Text Knowledge Base](https://docs.agno.com/knowledge/s3_text.md): Learn how to use text files from an S3 bucket in your knowledge base.
- [Agentic Search](https://docs.agno.com/knowledge/search.md)
- [Text Knowledge Base](https://docs.agno.com/knowledge/text.md): Learn how to use text files in your knowledge base.
- [Website Knowledge Base](https://docs.agno.com/knowledge/website.md): Learn how to use websites in your knowledge base.
- [Wikipedia KnowledgeBase](https://docs.agno.com/knowledge/wikipedia.md): Learn how to use Wikipedia topics in your knowledge base.
- [Youtube KnowledgeBase](https://docs.agno.com/knowledge/youtube.md): Learn how to use YouTube video transcripts in your knowledge base.
- [What is Memory?](https://docs.agno.com/memory/introduction.md): Memory gives an Agent the ability to recall relevant information.
- [User Memories](https://docs.agno.com/memory/memory.md)
- [Memory Storage](https://docs.agno.com/memory/storage.md)
- [AI/ML API](https://docs.agno.com/models/aimlapi.md): Learn how to use AI/ML API with Agno.
- [Anthropic Claude](https://docs.agno.com/models/anthropic.md): Learn how to use Anthropic Claude models in Agno.
- [AWS Bedrock](https://docs.agno.com/models/aws-bedrock.md): Learn how to use AWS Bedrock with Agno.
- [AWS Claude](https://docs.agno.com/models/aws-claude.md): Learn how to use AWS Claude models in Agno.
- [Azure AI Foundry](https://docs.agno.com/models/azure-ai-foundry.md): Learn how to use Azure AI Foundry models in Agno.
- [Azure OpenAI](https://docs.agno.com/models/azure-openai.md): Learn how to use Azure OpenAI models in Agno.
- [Cerebras](https://docs.agno.com/models/cerebras.md): Learn how to use Cerebras models in Agno.
- [Cerebras OpenAI](https://docs.agno.com/models/cerebras_openai.md): Learn how to use Cerebras OpenAI with Agno.
- [Cohere](https://docs.agno.com/models/cohere.md): Learn how to use Cohere models in Agno.
- [Models Compatibility](https://docs.agno.com/models/compatibility.md)
- [DeepInfra](https://docs.agno.com/models/deepinfra.md): Learn how to use DeepInfra models in Agno.
- [DeepSeek](https://docs.agno.com/models/deepseek.md): Learn how to use DeepSeek models in Agno.
- [Fireworks](https://docs.agno.com/models/fireworks.md): Learn how to use Fireworks models in Agno.
- [Gemini](https://docs.agno.com/models/google.md): Learn how to use Gemini models in Agno.
- [Groq](https://docs.agno.com/models/groq.md): Learn how to use Groq with Agno.
- [HuggingFace](https://docs.agno.com/models/huggingface.md): Learn how to use HuggingFace models in Agno.
- [IBM WatsonX](https://docs.agno.com/models/ibm-watsonx.md): Learn how to use IBM WatsonX models in Agno.
- [What are Models?](https://docs.agno.com/models/introduction.md): Language Models are machine-learning programs that are trained to understand natural language and code.
- [LiteLLM](https://docs.agno.com/models/litellm.md): Integrate LiteLLM with Agno for a unified LLM experience.
- [LiteLLM OpenAI](https://docs.agno.com/models/litellm_openai.md): Use LiteLLM with Agno with an openai-compatible proxy server.
- [LM Studio](https://docs.agno.com/models/lmstudio.md): Learn how to use LM Studio with Agno.
- [Meta](https://docs.agno.com/models/meta.md): Learn how to use Meta models in Agno.
- [Mistral](https://docs.agno.com/models/mistral.md): Learn how to use Mistral models in Agno.
- [Nebius](https://docs.agno.com/models/nebius.md): Learn how to use Nebius models in Agno.
- [Nvidia](https://docs.agno.com/models/nvidia.md): Learn how to use Nvidia models in Agno.
- [Ollama](https://docs.agno.com/models/ollama.md): Learn how to use Ollama with Agno.
- [OpenAI](https://docs.agno.com/models/openai.md): Learn how to use OpenAI models in Agno.
- [OpenAI Like](https://docs.agno.com/models/openai-like.md): Learn how to use OpenAI-like models in Agno.
- [OpenAI Responses](https://docs.agno.com/models/openai-responses.md): Learn how to use OpenAI Responses with Agno.
- [OpenRouter](https://docs.agno.com/models/openrouter.md): Learn how to use OpenRouter with Agno.
- [Perplexity](https://docs.agno.com/models/perplexity.md): Learn how to use Perplexity with Agno.
- [Sambanova](https://docs.agno.com/models/sambanova.md): Learn how to use Sambanova with Agno.
- [Together](https://docs.agno.com/models/together.md): Learn how to use Together with Agno.
- [Vercel v0](https://docs.agno.com/models/vercel.md): Learn how to use Vercel v0 models with Agno.
- [vLLM](https://docs.agno.com/models/vllm.md)
- [xAI](https://docs.agno.com/models/xai.md): Learn how to use xAI with Agno.
- [AgentOps](https://docs.agno.com/observability/agentops.md): Integrate Agno with AgentOps to send traces and logs to a centralized observability platform.
- [Arize](https://docs.agno.com/observability/arize.md): Integrate Agno with Arize Phoenix to send traces and gain insights into your agent's performance.
- [OpenTelemetry](https://docs.agno.com/observability/introduction.md): Agno supports observability through OpenTelemetry, integrating seamlessly with popular tracing and monitoring platforms.
- [Langfuse](https://docs.agno.com/observability/langfuse.md): Integrate Agno with Langfuse to send traces and gain insights into your agent's performance.
- [LangSmith](https://docs.agno.com/observability/langsmith.md): Integrate Agno with LangSmith to send traces and gain insights into your agent's performance.
- [Langtrace](https://docs.agno.com/observability/langtrace.md): Integrate Agno with Langtrace to send traces and gain insights into your agent's performance.
- [Weave](https://docs.agno.com/observability/weave.md): Integrate Agno with Weave by WandB to send traces and gain insights into your agent's performance.
- [What is Reasoning?](https://docs.agno.com/reasoning/introduction.md): Reasoning gives Agents the ability to "think" before responding and "analyze" the results of their actions (i.e. tool calls), greatly improving the Agents' ability to solve problems that require sequential tool calls.
- [Reasoning Agents](https://docs.agno.com/reasoning/reasoning-agents.md)
- [Reasoning Models](https://docs.agno.com/reasoning/reasoning-models.md)
- [Reasoning Tools](https://docs.agno.com/reasoning/reasoning-tools.md)
- [Agent](https://docs.agno.com/reference/agents/agent.md)
- [RunResponse](https://docs.agno.com/reference/agents/run-response.md)
- [AgentSession](https://docs.agno.com/reference/agents/session.md)
- [Agentic Chunking](https://docs.agno.com/reference/chunking/agentic.md)
- [Document Chunking](https://docs.agno.com/reference/chunking/document.md)
- [Fixed Size Chunking](https://docs.agno.com/reference/chunking/fixed-size.md)
- [Recursive Chunking](https://docs.agno.com/reference/chunking/recursive.md)
- [Semantic Chunking](https://docs.agno.com/reference/chunking/semantic.md)
- [Arxiv Reader](https://docs.agno.com/reference/document_reader/arxiv.md)
- [Reader](https://docs.agno.com/reference/document_reader/base.md)
- [CSV Reader](https://docs.agno.com/reference/document_reader/csv.md)
- [CSV URL Reader](https://docs.agno.com/reference/document_reader/csv_url.md)
- [Docx Reader](https://docs.agno.com/reference/document_reader/docx.md)
- [FireCrawl Reader](https://docs.agno.com/reference/document_reader/firecrawl.md)
- [JSON Reader](https://docs.agno.com/reference/document_reader/json.md)
- [PDF Reader](https://docs.agno.com/reference/document_reader/pdf.md)
- [PDF Image Reader](https://docs.agno.com/reference/document_reader/pdf_image.md)
- [PDF Image URL Reader](https://docs.agno.com/reference/document_reader/pdf_image_url.md)
- [PDF URL Reader](https://docs.agno.com/reference/document_reader/pdf_url.md)
- [Text Reader](https://docs.agno.com/reference/document_reader/text.md)
- [Website Reader](https://docs.agno.com/reference/document_reader/website.md)
- [YouTube Reader](https://docs.agno.com/reference/document_reader/youtube.md)
- [Azure OpenAI](https://docs.agno.com/reference/embedder/azure_openai.md)
- [Cohere](https://docs.agno.com/reference/embedder/cohere.md)
- [FastEmbed](https://docs.agno.com/reference/embedder/fastembed.md)
- [Fireworks](https://docs.agno.com/reference/embedder/fireworks.md)
- [Gemini](https://docs.agno.com/reference/embedder/gemini.md)
- [Hugging Face](https://docs.agno.com/reference/embedder/huggingface.md)
- [Mistral](https://docs.agno.com/reference/embedder/mistral.md)
- [Ollama](https://docs.agno.com/reference/embedder/ollama.md)
- [OpenAI](https://docs.agno.com/reference/embedder/openai.md)
- [Sentence Transformer](https://docs.agno.com/reference/embedder/sentence-transformer.md)
- [Together](https://docs.agno.com/reference/embedder/together.md)
- [VoyageAI](https://docs.agno.com/reference/embedder/voyageai.md)
- [Arxiv Knowledge Base](https://docs.agno.com/reference/knowledge/arxiv.md)
- [AgentKnowledge](https://docs.agno.com/reference/knowledge/base.md)
- [Combined Knowledge Base](https://docs.agno.com/reference/knowledge/combined.md)
- [CSV Knowledge Base](https://docs.agno.com/reference/knowledge/csv.md)
- [CSV URL Knowledge Base](https://docs.agno.com/reference/knowledge/csv_url.md)
- [Docx Knowledge Base](https://docs.agno.com/reference/knowledge/docx.md)
- [JSON Knowledge Base](https://docs.agno.com/reference/knowledge/json.md)
- [Langchain Knowledge Base](https://docs.agno.com/reference/knowledge/langchain.md)
- [LlamaIndex Knowledge Base](https://docs.agno.com/reference/knowledge/llamaindex.md)
- [PDF Knowledge Base](https://docs.agno.com/reference/knowledge/pdf.md)
- [PDF URL Knowledge Base](https://docs.agno.com/reference/knowledge/pdf_url.md)
- [Text Knowledge Base](https://docs.agno.com/reference/knowledge/text.md)
- [Website Knowledge Base](https://docs.agno.com/reference/knowledge/website.md)
- [Wikipedia Knowledge Base](https://docs.agno.com/reference/knowledge/wikipedia.md)
- [YouTube Knowledge Base](https://docs.agno.com/reference/knowledge/youtube.md)
- [Memory](https://docs.agno.com/reference/memory/memory.md)
- [MongoMemoryDb](https://docs.agno.com/reference/memory/storage/mongo.md)
- [PostgresMemoryDb](https://docs.agno.com/reference/memory/storage/postgres.md)
- [RedisMemoryDb](https://docs.agno.com/reference/memory/storage/redis.md)
- [SqliteMemoryDb](https://docs.agno.com/reference/memory/storage/sqlite.md)
- [AI/ML API](https://docs.agno.com/reference/models/aimlapi.md)
- [Claude](https://docs.agno.com/reference/models/anthropic.md)
- [Azure AI Foundry](https://docs.agno.com/reference/models/azure.md)
- [Azure OpenAI](https://docs.agno.com/reference/models/azure_open_ai.md)
- [AWS Bedrock](https://docs.agno.com/reference/models/bedrock.md): Learn how to use AWS Bedrock models in Agno.
- [AWS Bedrock Claude](https://docs.agno.com/reference/models/bedrock_claude.md)
- [Cohere](https://docs.agno.com/reference/models/cohere.md)
- [DeepInfra](https://docs.agno.com/reference/models/deepinfra.md)
- [DeepSeek](https://docs.agno.com/reference/models/deepseek.md)
- [Fireworks](https://docs.agno.com/reference/models/fireworks.md)
- [Gemini](https://docs.agno.com/reference/models/gemini.md)
- [Groq](https://docs.agno.com/reference/models/groq.md)
- [HuggingFace](https://docs.agno.com/reference/models/huggingface.md)
- [IBM WatsonX](https://docs.agno.com/reference/models/ibm-watsonx.md)
- [InternLM](https://docs.agno.com/reference/models/internlm.md)
- [Meta](https://docs.agno.com/reference/models/meta.md)
- [Mistral](https://docs.agno.com/reference/models/mistral.md)
- [Model](https://docs.agno.com/reference/models/model.md)
- [Nebius](https://docs.agno.com/reference/models/nebius.md)
- [Nvidia](https://docs.agno.com/reference/models/nvidia.md)
- [Ollama](https://docs.agno.com/reference/models/ollama.md)
- [Ollama Tools](https://docs.agno.com/reference/models/ollama_tools.md)
- [OpenAI](https://docs.agno.com/reference/models/openai.md)
- [OpenAI Like](https://docs.agno.com/reference/models/openai_like.md)
- [OpenRouter](https://docs.agno.com/reference/models/openrouter.md)
- [Perplexity](https://docs.agno.com/reference/models/perplexity.md)
- [Sambanova](https://docs.agno.com/reference/models/sambanova.md)
- [Together](https://docs.agno.com/reference/models/together.md)
- [Vercel v0](https://docs.agno.com/reference/models/vercel.md)
- [xAI](https://docs.agno.com/reference/models/xai.md)
- [Cohere Reranker](https://docs.agno.com/reference/reranker/cohere.md)
- [DynamoDB](https://docs.agno.com/reference/storage/dynamodb.md)
- [JSON](https://docs.agno.com/reference/storage/json.md)
- [MongoDB](https://docs.agno.com/reference/storage/mongodb.md)
- [MySQL](https://docs.agno.com/reference/storage/mysql.md)
- [PostgreSQL](https://docs.agno.com/reference/storage/postgres.md)
- [SingleStore](https://docs.agno.com/reference/storage/singlestore.md)
- [SQLite](https://docs.agno.com/reference/storage/sqlite.md)
- [YAML](https://docs.agno.com/reference/storage/yaml.md)
- [Team Session](https://docs.agno.com/reference/teams/session.md)
- [Team](https://docs.agno.com/reference/teams/team.md)
- [TeamRunResponse](https://docs.agno.com/reference/teams/team-response.md)
- [Cassandra](https://docs.agno.com/reference/vector_db/cassandra.md)
- [ChromaDb](https://docs.agno.com/reference/vector_db/chromadb.md)
- [Clickhouse](https://docs.agno.com/reference/vector_db/clickhouse.md)
- [Couchbase](https://docs.agno.com/reference/vector_db/couchbase.md)
- [LanceDb](https://docs.agno.com/reference/vector_db/lancedb.md)
- [Milvus](https://docs.agno.com/reference/vector_db/milvus.md)
- [MongoDb](https://docs.agno.com/reference/vector_db/mongodb.md)
- [PgVector](https://docs.agno.com/reference/vector_db/pgvector.md)
- [Pinecone](https://docs.agno.com/reference/vector_db/pinecone.md)
- [Qdrant](https://docs.agno.com/reference/vector_db/qdrant.md)
- [SingleStore](https://docs.agno.com/reference/vector_db/singlestore.md)
- [Weaviate](https://docs.agno.com/reference/vector_db/weaviate.md)
- [MongoDB Workflow Storage](https://docs.agno.com/reference/workflows/storage/mongodb.md)
- [Postgres Workflow Storage](https://docs.agno.com/reference/workflows/storage/postgres.md)
- [SQLite Workflow Storage](https://docs.agno.com/reference/workflows/storage/sqlite.md)
- [Workflow](https://docs.agno.com/reference/workflows/workflow.md)
- [DynamoDB Storage](https://docs.agno.com/storage/dynamodb.md)
- [What is Storage?](https://docs.agno.com/storage/introduction.md): Storage is a way to persist Agent sessions and state to a database or file.
- [JSON Storage](https://docs.agno.com/storage/json.md)
- [Mongo Storage](https://docs.agno.com/storage/mongodb.md)
- [MySQL Storage](https://docs.agno.com/storage/mysql.md)
- [Postgres Storage](https://docs.agno.com/storage/postgres.md)
- [Redis Storage](https://docs.agno.com/storage/redis.md)
- [Singlestore Storage](https://docs.agno.com/storage/singlestore.md)
- [Sqlite Storage](https://docs.agno.com/storage/sqlite.md)
- [YAML Storage](https://docs.agno.com/storage/yaml.md)
- [Collaborate](https://docs.agno.com/teams/collaborate.md)
- [Coordinate](https://docs.agno.com/teams/coordinate.md)
- [What are Teams?](https://docs.agno.com/teams/introduction.md): Build autonomous multi-agent systems with Agno Teams.
- [Metrics](https://docs.agno.com/teams/metrics.md): Understanding team run and session metrics in Agno
- [Route](https://docs.agno.com/teams/route.md)
- [Running your Team](https://docs.agno.com/teams/run.md): Learn how to run a team and get the response.
- [Team State](https://docs.agno.com/teams/shared-state.md): Learn about the shared state of Agent Teams.
- [Structured Output](https://docs.agno.com/teams/structured-output.md)
- [Scenario Testing](https://docs.agno.com/testing/scenario-testing.md)
- [Async Tools](https://docs.agno.com/tools/async-tools.md)
- [Updating Tools](https://docs.agno.com/tools/attaching-tools.md): Learn how to add/update tools on Agents and Teams after they have been created.
- [Tool Result Caching](https://docs.agno.com/tools/caching.md)
- [Writing your own Toolkit](https://docs.agno.com/tools/custom-toolkits.md)
- [Exceptions](https://docs.agno.com/tools/exceptions.md)
- [Human in the loop](https://docs.agno.com/tools/hitl.md)
- [Hooks](https://docs.agno.com/tools/hooks.md): Learn how to use tool hooks to modify the behavior of a tool.
- [What are Tools?](https://docs.agno.com/tools/introduction.md): Tools are functions that helps Agno Agents to interact with the external world.
- [Advanced MCP Usage](https://docs.agno.com/tools/mcp/advanced_usage.md)
- [Model Context Protocol (MCP)](https://docs.agno.com/tools/mcp/mcp.md): Learn how to use MCP with Agno to enable your agents to interact with external systems through a standardized interface.
- [SSE Transport](https://docs.agno.com/tools/mcp/transports/sse.md)
- [Stdio Transport](https://docs.agno.com/tools/mcp/transports/stdio.md)
- [Streamable HTTP Transport](https://docs.agno.com/tools/mcp/transports/streamable_http.md)
- [Knowledge Tools](https://docs.agno.com/tools/reasoning_tools/knowledge-tools.md)
- [Reasoning Tools](https://docs.agno.com/tools/reasoning_tools/reasoning-tools.md)
- [Thinking Tools](https://docs.agno.com/tools/reasoning_tools/thinking-tools.md)
- [Selecting tools](https://docs.agno.com/tools/selecting-tools.md)
- [Writing your own tools](https://docs.agno.com/tools/tool-decorator.md): Learn how to write your own tools and how to use the `@tool` decorator to modify the behavior of a tool.
- [Tool Call Limit](https://docs.agno.com/tools/tool_call_limit.md): Learn to limit the number of tool calls an agent can make.
- [CSV](https://docs.agno.com/tools/toolkits/database/csv.md)
- [DuckDb](https://docs.agno.com/tools/toolkits/database/duckdb.md)
- [Pandas](https://docs.agno.com/tools/toolkits/database/pandas.md)
- [Postgres](https://docs.agno.com/tools/toolkits/database/postgres.md)
- [SQL](https://docs.agno.com/tools/toolkits/database/sql.md)
- [Zep](https://docs.agno.com/tools/toolkits/database/zep.md)
- [Calculator](https://docs.agno.com/tools/toolkits/local/calculator.md)
- [Docker](https://docs.agno.com/tools/toolkits/local/docker.md)
- [File](https://docs.agno.com/tools/toolkits/local/file.md)
- [Python](https://docs.agno.com/tools/toolkits/local/python.md)
- [Shell](https://docs.agno.com/tools/toolkits/local/shell.md)
- [Sleep](https://docs.agno.com/tools/toolkits/local/sleep.md)
- [Gemini](https://docs.agno.com/tools/toolkits/models/gemini.md)
- [Groq](https://docs.agno.com/tools/toolkits/models/groq.md)
- [OpenAI](https://docs.agno.com/tools/toolkits/models/openai.md)
- [Airflow](https://docs.agno.com/tools/toolkits/others/airflow.md)
- [Apify](https://docs.agno.com/tools/toolkits/others/apify.md)
- [AWS Lambda](https://docs.agno.com/tools/toolkits/others/aws_lambda.md)
- [AWS SES](https://docs.agno.com/tools/toolkits/others/aws_ses.md)
- [Cal.com](https://docs.agno.com/tools/toolkits/others/calcom.md)
- [Cartesia](https://docs.agno.com/tools/toolkits/others/cartesia.md): Tools for interacting with Cartesia Voice AI services including text-to-speech and voice localization
- [Composio](https://docs.agno.com/tools/toolkits/others/composio.md)
- [Confluence](https://docs.agno.com/tools/toolkits/others/confluence.md)
- [Custom API](https://docs.agno.com/tools/toolkits/others/custom_api.md)
- [Dalle](https://docs.agno.com/tools/toolkits/others/dalle.md)
- [Daytona](https://docs.agno.com/tools/toolkits/others/daytona.md): Enable your Agents to run code in a remote, secure sandbox.
- [E2B](https://docs.agno.com/tools/toolkits/others/e2b.md): Enable your Agents to run code in a remote, secure sandbox.
- [Eleven Labs](https://docs.agno.com/tools/toolkits/others/eleven_labs.md)
- [Fal](https://docs.agno.com/tools/toolkits/others/fal.md)
- [Financial Datasets API](https://docs.agno.com/tools/toolkits/others/financial_datasets.md)
- [Giphy](https://docs.agno.com/tools/toolkits/others/giphy.md)
- [Github](https://docs.agno.com/tools/toolkits/others/github.md)
- [Google Maps](https://docs.agno.com/tools/toolkits/others/google_maps.md): Tools for interacting with Google Maps services including place search, directions, geocoding, and more
- [Google Sheets](https://docs.agno.com/tools/toolkits/others/google_sheets.md)
- [Google Calendar](https://docs.agno.com/tools/toolkits/others/googlecalendar.md)
- [Jira](https://docs.agno.com/tools/toolkits/others/jira.md)
- [Linear](https://docs.agno.com/tools/toolkits/others/linear.md)
- [Lumalabs](https://docs.agno.com/tools/toolkits/others/lumalabs.md)
- [MLX Transcribe](https://docs.agno.com/tools/toolkits/others/mlx_transcribe.md)
- [ModelsLabs](https://docs.agno.com/tools/toolkits/others/models_labs.md)
- [MoviePy Video Tools](https://docs.agno.com/tools/toolkits/others/moviepy.md): Agno MoviePyVideoTools enable an Agent to process videos, extract audio, generate SRT caption files, and embed rich, word-highlighted captions.
- [OpenBB](https://docs.agno.com/tools/toolkits/others/openbb.md)
- [OpenWeather](https://docs.agno.com/tools/toolkits/others/openweather.md)
- [Replicate](https://docs.agno.com/tools/toolkits/others/replicate.md)
- [Resend](https://docs.agno.com/tools/toolkits/others/resend.md)
- [Todoist](https://docs.agno.com/tools/toolkits/others/todoist.md)
- [Trello](https://docs.agno.com/tools/toolkits/others/trello.md): Agno TrelloTools helps to integrate Trello functionalities into your agents, enabling management of boards, lists, and cards.
- [Web Browser Tools](https://docs.agno.com/tools/toolkits/others/web-browser.md): WebBrowser Tools enable an Agent to open a URL in a web browser.
- [Yfinance](https://docs.agno.com/tools/toolkits/others/yfinance.md)
- [Youtube](https://docs.agno.com/tools/toolkits/others/youtube.md)
- [Zendesk](https://docs.agno.com/tools/toolkits/others/zendesk.md)
- [Arxiv](https://docs.agno.com/tools/toolkits/search/arxiv.md)
- [BaiduSearch](https://docs.agno.com/tools/toolkits/search/baidusearch.md)
- [Brave Search](https://docs.agno.com/tools/toolkits/search/bravesearch.md)
- [DuckDuckGo](https://docs.agno.com/tools/toolkits/search/duckduckgo.md)
- [Exa](https://docs.agno.com/tools/toolkits/search/exa.md)
- [Google Search](https://docs.agno.com/tools/toolkits/search/googlesearch.md)
- [Hacker News](https://docs.agno.com/tools/toolkits/search/hackernews.md)
- [Pubmed](https://docs.agno.com/tools/toolkits/search/pubmed.md)
- [Searxng](https://docs.agno.com/tools/toolkits/search/searxng.md)
- [Serpapi](https://docs.agno.com/tools/toolkits/search/serpapi.md)
- [SerperApi](https://docs.agno.com/tools/toolkits/search/serper.md)
- [Tavily](https://docs.agno.com/tools/toolkits/search/tavily.md)
- [Wikipedia](https://docs.agno.com/tools/toolkits/search/wikipedia.md)
- [Discord](https://docs.agno.com/tools/toolkits/social/discord.md)
- [Email](https://docs.agno.com/tools/toolkits/social/email.md)
- [Gmail](https://docs.agno.com/tools/toolkits/social/gmail.md)
- [Slack](https://docs.agno.com/tools/toolkits/social/slack.md)
- [Telegram](https://docs.agno.com/tools/toolkits/social/telegram.md)
- [Twilio](https://docs.agno.com/tools/toolkits/social/twilio.md)
- [Webex](https://docs.agno.com/tools/toolkits/social/webex.md)
- [WhatsApp](https://docs.agno.com/tools/toolkits/social/whatsapp.md)
- [X (Twitter)](https://docs.agno.com/tools/toolkits/social/x.md)
- [Zoom](https://docs.agno.com/tools/toolkits/social/zoom.md)
- [Toolkit Index](https://docs.agno.com/tools/toolkits/toolkits.md)
- [AgentQL](https://docs.agno.com/tools/toolkits/web_scrape/agentql.md)
- [BrightData](https://docs.agno.com/tools/toolkits/web_scrape/brightdata.md): BrightDataTools enable an Agent to perform web scraping, search engine queries, screenshots, and structured data extraction using BrightData's API.
- [Browserbase](https://docs.agno.com/tools/toolkits/web_scrape/browserbase.md)
- [Crawl4AI](https://docs.agno.com/tools/toolkits/web_scrape/crawl4ai.md)
- [Firecrawl](https://docs.agno.com/tools/toolkits/web_scrape/firecrawl.md): Use Firecrawl with Agno to scrape and crawl the web.
- [Jina Reader](https://docs.agno.com/tools/toolkits/web_scrape/jina_reader.md)
- [Newspaper](https://docs.agno.com/tools/toolkits/web_scrape/newspaper.md)
- [Newspaper4k](https://docs.agno.com/tools/toolkits/web_scrape/newspaper4k.md)
- [Oxylabs](https://docs.agno.com/tools/toolkits/web_scrape/oxylabs.md)
- [ScrapeGraph](https://docs.agno.com/tools/toolkits/web_scrape/scrapegraph.md): Agno ScrapeGraphTools enable an Agent to extract structured data from webpages for LLMs in markdown format.
- [Spider](https://docs.agno.com/tools/toolkits/web_scrape/spider.md)
- [Website Tools](https://docs.agno.com/tools/toolkits/web_scrape/website.md)
- [Azure Cosmos DB MongoDB vCore Agent Knowledge](https://docs.agno.com/vectordb/azure_cosmos_mongodb.md)
- [Cassandra Agent Knowledge](https://docs.agno.com/vectordb/cassandra.md)
- [ChromaDB Agent Knowledge](https://docs.agno.com/vectordb/chroma.md)
- [Clickhouse Agent Knowledge](https://docs.agno.com/vectordb/clickhouse.md)
- [Couchbase Agent Knowledge](https://docs.agno.com/vectordb/couchbase.md)
- [What are Vector Databases?](https://docs.agno.com/vectordb/introduction.md): Vector databases enable us to store information as embeddings and search for "results similar" to our input query using cosine similarity or full text search. These results are then provided to the Agent as context so it can respond in a context-aware manner using Retrieval Augmented Generation (RAG).
- [LanceDB Agent Knowledge](https://docs.agno.com/vectordb/lancedb.md)
- [Milvus Agent Knowledge](https://docs.agno.com/vectordb/milvus.md)
- [MongoDB Agent Knowledge](https://docs.agno.com/vectordb/mongodb.md)
- [PgVector Agent Knowledge](https://docs.agno.com/vectordb/pgvector.md)
- [Pinecone Agent Knowledge](https://docs.agno.com/vectordb/pinecone.md)
- [Qdrant Agent Knowledge](https://docs.agno.com/vectordb/qdrant.md)
- [SingleStore Agent Knowledge](https://docs.agno.com/vectordb/singlestore.md)
- [Weaviate Agent Knowledge](https://docs.agno.com/vectordb/weaviate.md)
- [Advanced](https://docs.agno.com/workflows/advanced.md)
- [What are Workflows?](https://docs.agno.com/workflows/introduction.md)
- [Workflow State](https://docs.agno.com/workflows/state.md)
- [Running the Agent API on AWS](https://docs.agno.com/workspaces/agent-api/aws.md)
- [Agent API: FastAPI and Postgres](https://docs.agno.com/workspaces/agent-api/local.md)
- [Running the Agent App on AWS](https://docs.agno.com/workspaces/agent-app/aws.md)
- [Agent App: FastAPI, Streamlit and Postgres](https://docs.agno.com/workspaces/agent-app/local.md)
- [Standardized Codebases for Agentic Systems](https://docs.agno.com/workspaces/introduction.md)
- [CI/CD](https://docs.agno.com/workspaces/workspace-management/ci-cd.md)
- [Database Tables](https://docs.agno.com/workspaces/workspace-management/database-tables.md)
- [Development Application](https://docs.agno.com/workspaces/workspace-management/development-app.md)
- [Use Custom Domain and HTTPS](https://docs.agno.com/workspaces/workspace-management/domain-https.md)
- [Environment variables](https://docs.agno.com/workspaces/workspace-management/env-vars.md)
- [Format & Validate](https://docs.agno.com/workspaces/workspace-management/format-and-validate.md)
- [Create Git Repo](https://docs.agno.com/workspaces/workspace-management/git-repo.md)
- [Install & Setup](https://docs.agno.com/workspaces/workspace-management/install.md)
- [Introduction](https://docs.agno.com/workspaces/workspace-management/introduction.md)
- [Setup workspace for new users](https://docs.agno.com/workspaces/workspace-management/new-users.md)
- [Production Application](https://docs.agno.com/workspaces/workspace-management/production-app.md)
- [Add Python Libraries](https://docs.agno.com/workspaces/workspace-management/python-packages.md)
- [Add Secrets](https://docs.agno.com/workspaces/workspace-management/secrets.md)
- [SSH Access](https://docs.agno.com/workspaces/workspace-management/ssh-access.md)
- [Workspace Settings](https://docs.agno.com/workspaces/workspace-management/workspace-settings.md)
